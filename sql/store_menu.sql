-- 经销商管理菜单 SQL
-- 菜单 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商管理', '1', '10', 'store', 'yanbao/store/index', 1, 0, 'C', '0', '0', 'yanbao:store:list', 'peoples', 'admin', sysdate(), '', null, '经销商管理菜单');

-- 按钮父菜单ID
SELECT @parentId := LAST_INSERT_ID();

-- 按钮 SQL
insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商查询', @parentId, '1',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:store:query',        '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商新增', @parentId, '2',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:store:add',          '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商修改', @parentId, '3',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:store:edit',         '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商删除', @parentId, '4',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:store:remove',       '#', 'admin', sysdate(), '', null, '');

insert into sys_menu (menu_name, parent_id, order_num, path, component, is_frame, is_cache, menu_type, visible, status, perms, icon, create_by, create_time, update_by, update_time, remark)
values('经销商导出', @parentId, '5',  '#', '', 1, 0, 'F', '0', '0', 'yanbao:store:export',       '#', 'admin', sysdate(), '', null, '');
