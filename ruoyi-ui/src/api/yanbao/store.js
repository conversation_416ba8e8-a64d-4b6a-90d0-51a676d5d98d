import request from '@/utils/request.js'

// 查询经销商管理列表
export function listStore(query) {
  return request({
    url: '/yanbao/store/list',
    method: 'get',
    params: query
  })
}

// 查询经销商管理详细
export function getStore(id) {
  return request({
    url: '/yanbao/store/' + id,
    method: 'get'
  })
}

// 新增经销商管理
export function addStore(data) {
  return request({
    url: '/yanbao/store',
    method: 'post',
    data: data
  })
}

// 修改经销商管理
export function updateStore(data) {
  return request({
    url: '/yanbao/store',
    method: 'put',
    data: data
  })
}

// 删除经销商管理
export function delStore(id) {
  return request({
    url: '/yanbao/store/' + id,
    method: 'delete'
  })
}

// 经销商状态修改
export function changeStoreStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/yanbao/store/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取经销商选择框列表
export function optionSelectStore(status) {
  return request({
    url: '/yanbao/store/optionSelect',
    method: 'get',
    params: { status }
  })
}

// 经销商排序
export function updateStoreSort(data) {
  return request({
    url: '/yanbao/store/sort',
    method: 'put',
    data: data
  })
}
