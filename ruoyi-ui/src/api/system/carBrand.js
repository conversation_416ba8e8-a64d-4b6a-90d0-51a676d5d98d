import request from '@/utils/request'

// 查询品牌管理列表
export function listCarBrand(query) {
  return request({
    url: '/system/carBrand/list',
    method: 'get',
    params: query
  })
}

// 查询品牌管理详细
export function getCarBrand(id) {
  return request({
    url: '/system/carBrand/' + id,
    method: 'get'
  })
}

// 新增品牌管理
export function addCarBrand(data) {
  return request({
    url: '/system/carBrand',
    method: 'post',
    data: data
  })
}

// 修改品牌管理
export function updateCarBrand(data) {
  return request({
    url: '/system/carBrand',
    method: 'put',
    data: data
  })
}

// 删除品牌管理
export function delCarBrand(id) {
  return request({
    url: '/system/carBrand/' + id,
    method: 'delete'
  })
}

// 品牌状态修改
export function changeCarBrandStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/carBrand/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取品牌选择框列表
export function optionSelectCarBrand() {
  return request({
    url: '/system/carBrand/optionSelect',
    method: 'get'
  })
}

// 批量更新品牌排序
export function updateCarBrandSort(data) {
  return request({
    url: '/system/carBrand/sort',
    method: 'put',
    data: data
  })
}
