import request from '@/utils/request'

// 查询车系管理列表
export function listCarSeries(query) {
  return request({
    url: '/system/carSeries/list',
    method: 'get',
    params: query
  })
}

// 查询车系管理详细
export function getCarSeries(id) {
  return request({
    url: '/system/carSeries/' + id,
    method: 'get'
  })
}

// 新增车系管理
export function addCarSeries(data) {
  return request({
    url: '/system/carSeries',
    method: 'post',
    data: data
  })
}

// 修改车系管理
export function updateCarSeries(data) {
  return request({
    url: '/system/carSeries',
    method: 'put',
    data: data
  })
}

// 删除车系管理
export function delCarSeries(id) {
  return request({
    url: '/system/carSeries/' + id,
    method: 'delete'
  })
}

// 车系状态修改
export function changeCarSeriesStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
    url: '/system/carSeries/changeStatus',
    method: 'put',
    data: data
  })
}

// 获取车系选择框列表
export function optionSelectCarSeries() {
  return request({
    url: '/system/carSeries/optionSelect',
    method: 'get'
  })
}

// 批量更新车系排序
export function updateCarSeriesSort(data) {
  return request({
    url: '/system/carSeries/sort',
    method: 'put',
    data: data
  })
}
