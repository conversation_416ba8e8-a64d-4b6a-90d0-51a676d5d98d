package com.ruoyi.web.controller.yanbao;

import java.util.List;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 经销商管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/yanbao/store")
public class StoreController extends BaseController
{
    @Autowired
    private StoreService storeService;

    /**
     * 查询经销商管理列表
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:list')")
    @GetMapping("/list")
    public TableDataInfo list(Store store)
    {
        startPage();
        List<Store> list = storeService.selectStoreList(store);
        return getDataTable(list);
    }

    /**
     * 获取经销商管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(storeService.getById(id));
    }

    /**
     * 新增经销商管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:add')")
    @Log(title = "经销商管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Store store)
    {
        if (!storeService.checkStoreNameUnique(store))
        {
            return error("新增经销商'" + store.getName() + "'失败，经销商名称已存在");
        }
        store.setCreatedBy(getUsername());
        return toAjax(storeService.save(store));
    }

    /**
     * 修改经销商管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:edit')")
    @Log(title = "经销商管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Store store)
    {
        if (!storeService.checkStoreNameUnique(store))
        {
            return error("修改经销商'" + store.getName() + "'失败，经销商名称已存在");
        }
        store.setChangedBy(getUsername());
        return toAjax(storeService.updateById(store));
    }

    /**
     * 删除经销商管理
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:remove')")
    @Log(title = "经销商管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(storeService.deleteStoreByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:edit')")
    @Log(title = "经销商管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody Store store)
    {
        return toAjax(storeService.changeStatus(store.getId(), store.getStatus()));
    }

    /**
     * 获取经销商选择框列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect(Integer status)
    {
        List<Store> stores = storeService.selectStoreAll(status);
        return success(stores);
    }

    /**
     * 排序操作
     */
    @PreAuthorize("@ss.hasPermi('yanbao:store:edit')")
    @Log(title = "经销商管理", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public AjaxResult updateSort(@RequestBody List<Store> storeList)
    {
        return toAjax(storeService.updateStoreSort(storeList));
    }
}
