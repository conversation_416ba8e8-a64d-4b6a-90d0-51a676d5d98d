package com.ruoyi.web.controller.system;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.CarBrand;
import com.ruoyi.yanbao.service.CarBrandService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 品牌管理Controller
 * 
 * <AUTHOR>
 * @date 2025-07-02
 */
@RestController
@RequestMapping("/system/carBrand")
public class CarBrandController extends BaseController
{
    @Autowired
    private CarBrandService carBrandService;

    /**
     * 查询品牌管理列表
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:list')")
    @GetMapping("/list")
    public TableDataInfo list(CarBrand carBrand)
    {
        startPage();
        List<CarBrand> list = carBrandService.selectCarBrandList(carBrand);
        return getDataTable(list);
    }

    /**
     * 获取品牌管理详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(carBrandService.getById(id));
    }

    /**
     * 新增品牌管理
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:add')")
    @Log(title = "品牌管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody CarBrand carBrand)
    {
        if (!carBrandService.checkCarBrandNameUnique(carBrand))
        {
            return error("新增品牌'" + carBrand.getName() + "'失败，品牌名称已存在");
        }
        carBrand.setCreatedBy(getUsername());
        return toAjax(carBrandService.save(carBrand));
    }

    /**
     * 修改品牌管理
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:edit')")
    @Log(title = "品牌管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody CarBrand carBrand)
    {
        if (!carBrandService.checkCarBrandNameUnique(carBrand))
        {
            return error("修改品牌'" + carBrand.getName() + "'失败，品牌名称已存在");
        }
        carBrand.setChangedBy(getUsername());
        return toAjax(carBrandService.updateById(carBrand));
    }

    /**
     * 删除品牌管理
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:remove')")
    @Log(title = "品牌管理", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(carBrandService.deleteCarBrandByIds(ids));
    }

    /**
     * 状态修改
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:edit')")
    @Log(title = "品牌管理", businessType = BusinessType.UPDATE)
    @PutMapping("/changeStatus")
    public AjaxResult changeStatus(@RequestBody CarBrand carBrand)
    {
        return toAjax(carBrandService.changeStatus(carBrand.getId(),carBrand.getStatus()));
    }

    /**
     * 获取品牌选择框列表
     */
    @GetMapping("/optionSelect")
    public AjaxResult optionSelect(Integer status)
    {
        List<CarBrand> carBrands = carBrandService.selectCarBrandAll(status);
        return success(carBrands);
    }

    /**
     * 排序操作
     */
    @PreAuthorize("@ss.hasPermi('system:carBrand:edit')")
    @Log(title = "品牌管理", businessType = BusinessType.UPDATE)
    @PutMapping("/sort")
    public AjaxResult updateSort(@RequestBody List<CarBrand> carBrandList)
    {
        return toAjax(carBrandService.updateCarBrandSort(carBrandList));
    }
}
