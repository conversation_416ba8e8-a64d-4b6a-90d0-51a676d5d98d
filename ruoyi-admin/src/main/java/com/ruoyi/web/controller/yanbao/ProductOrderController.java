package com.ruoyi.web.controller.yanbao;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.yanbao.entity.ProductOrder;
import com.ruoyi.yanbao.entity.vo.ProductOrderVo;
import com.ruoyi.yanbao.service.ProductOrderService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/yanbao/order")
public class ProductOrderController extends BaseController {

    @Autowired
    private ProductOrderService productOrderService;

    @PreAuthorize("@ss.hasPermi('yanbao:order:add')")
    @Log(title = "新增订单", businessType = BusinessType.INSERT)
    @PostMapping("add")
    public AjaxResult add(@RequestBody ProductOrderVo order) {
        ProductOrder po = (ProductOrder) order;
        po.setSaleUserId(getUserId());
        po.setSaleUserName(getLoginUser().getUser().getNickName());
        po.setCreatedBy(getLoginUser().getUser().getNickName());
        po.setAuditState(ProductOrderVo.AuditState.WAIT);
        //提交校验必填项
        if (ProductOrderVo.SubmitState.SUBMITTED.equals(po.getSubmitState())){

        }
        return toAjax(productOrderService.save(po));
    }
}
