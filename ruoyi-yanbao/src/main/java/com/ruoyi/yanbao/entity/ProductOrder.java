package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@TableName("p_product_order")
public class ProductOrder implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1：已删除，0：未删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 业务员id
     */
    private Long saleUserId;

    /**
     * 业务员姓名
     */
    private String saleUserName;

    /**
     * 产品ID
     */
    private Long productId;

    /**
     * 证件类型：1：身份证，2：营业执照
     */
    private Integer certificateType;

    /**
     * 证件图片
     */
    private String certificateImg;

    /**
     * 客户姓名
     */
    private String customerName;

    /**
     * 证件号
     */
    private String certificateNo;

    /**
     * 联系电话
     */
    private String contactTelephone;

    /**
     * 行驶证图片
     */
    private String vehicleLicenseImg;

    /**
     * 车架号
     */
    private String vinNo;

    /**
     * 发动机号
     */
    private String engineNo;

    /**
     * 车牌号
     */
    private String carNo;

    /**
     * 注册日期
     */
    private Date registrationDate;

    /**
     * 发证日期
     */
    private Date issueDate;

    /**
     * 车辆使用性质：1：非营运，2：营运
     */
    private Integer carUseage;

    /**
     * 车辆品牌ID
     */
    private Long carBrandId;

    /**
     * 车辆型号
     */
    private String carSeries;

    /**
     * 动力类型：1：燃油（油混）汽车，2：纯电动汽车，3：插电混合动力汽车，4：增程式电动汽车
     */
    private Integer powerType;

    /**
     * 购车发票
     */
    private String carInvoiceImg;

    /**
     * 购车金额
     */
    private BigDecimal carPrice;

    /**
     * 购车日期
     */
    private Date carBuyDate;

    /**
     * 左前45°图片
     */
    private String leftImg;

    /**
     * 右前45°图片
     */
    private String rightImg;

    /**
     * 左后45°图片
     */
    private String leftbackImg;

    /**
     * 右后45°图片
     */
    private String rightbackImg;

    /**
     * 车架号图片
     */
    private String vinImg;

    /**
     * 行驶里程图片
     */
    private String carMileageImg;

    /**
     * 行驶里程
     */
    private Long carMileage;

    /**
     * 车船税金额
     */
    private BigDecimal vehicleTaxPrice;

    /**
     * 购置税金额
     */
    private BigDecimal purchaseTaxPrice;

    /**
     * 完税证明图片
     */
    private String purchaseTaxCompleteImg;

    /**
     * 车辆上牌费用
     */
    private BigDecimal vehicleLicensePrice;

    /**
     * 上牌费用发票
     */
    private String vehicleLicenseInvoiceImg;

    /**
     * 交强险保单
     */
    private String trafficInsuranceImg;

    /**
     * 服务生效日期
     */
    private Date serviceEnableDate;

    /**
     * 服务期限（月）
     */
    private Integer servicePeriod;

    /**
     * 经销商ID
     */
    private Long storeId;

    /**
     * 经销商名称
     */
    private String storeName;

    /**
     * 付款小票单号
     */
    private String payNo;

    /**
     * 付款小票金额
     */
    private BigDecimal payPrice;

    /**
     * 付款小票图片
     */
    private String payImg;

    /**
     * 备注
     */
    private String remark;

    /**
     * 提交状态：0：暂存，1：已提交
     */
    private Integer submitState;

    /**
     * 审核状态：0：未审核，1：审核通过，2：驳回
     */
    private Integer auditState;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public Long getSaleUserId() {
        return saleUserId;
    }

    public void setSaleUserId(Long saleUserId) {
        this.saleUserId = saleUserId;
    }

    public String getSaleUserName() {
        return saleUserName;
    }

    public void setSaleUserName(String saleUserName) {
        this.saleUserName = saleUserName;
    }

    public Long getProductId() {
        return productId;
    }

    public void setProductId(Long productId) {
        this.productId = productId;
    }

    public Integer getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(Integer certificateType) {
        this.certificateType = certificateType;
    }

    public String getCertificateImg() {
        return certificateImg;
    }

    public void setCertificateImg(String certificateImg) {
        this.certificateImg = certificateImg;
    }

    public String getCustomerName() {
        return customerName;
    }

    public void setCustomerName(String customerName) {
        this.customerName = customerName;
    }

    public String getCertificateNo() {
        return certificateNo;
    }

    public void setCertificateNo(String certificateNo) {
        this.certificateNo = certificateNo;
    }

    public String getContactTelephone() {
        return contactTelephone;
    }

    public void setContactTelephone(String contactTelephone) {
        this.contactTelephone = contactTelephone;
    }

    public String getVehicleLicenseImg() {
        return vehicleLicenseImg;
    }

    public void setVehicleLicenseImg(String vehicleLicenseImg) {
        this.vehicleLicenseImg = vehicleLicenseImg;
    }

    public String getVinNo() {
        return vinNo;
    }

    public void setVinNo(String vinNo) {
        this.vinNo = vinNo;
    }

    public String getEngineNo() {
        return engineNo;
    }

    public void setEngineNo(String engineNo) {
        this.engineNo = engineNo;
    }

    public String getCarNo() {
        return carNo;
    }

    public void setCarNo(String carNo) {
        this.carNo = carNo;
    }

    public Date getRegistrationDate() {
        return registrationDate;
    }

    public void setRegistrationDate(Date registrationDate) {
        this.registrationDate = registrationDate;
    }

    public Date getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(Date issueDate) {
        this.issueDate = issueDate;
    }

    public Integer getCarUseage() {
        return carUseage;
    }

    public void setCarUseage(Integer carUseage) {
        this.carUseage = carUseage;
    }

    public Long getCarBrandId() {
        return carBrandId;
    }

    public void setCarBrandId(Long carBrandId) {
        this.carBrandId = carBrandId;
    }

    public String getCarSeries() {
        return carSeries;
    }

    public void setCarSeries(String carSeries) {
        this.carSeries = carSeries;
    }

    public Integer getPowerType() {
        return powerType;
    }

    public void setPowerType(Integer powerType) {
        this.powerType = powerType;
    }

    public String getCarInvoiceImg() {
        return carInvoiceImg;
    }

    public void setCarInvoiceImg(String carInvoiceImg) {
        this.carInvoiceImg = carInvoiceImg;
    }

    public BigDecimal getCarPrice() {
        return carPrice;
    }

    public void setCarPrice(BigDecimal carPrice) {
        this.carPrice = carPrice;
    }

    public Date getCarBuyDate() {
        return carBuyDate;
    }

    public void setCarBuyDate(Date carBuyDate) {
        this.carBuyDate = carBuyDate;
    }

    public String getLeftImg() {
        return leftImg;
    }

    public void setLeftImg(String leftImg) {
        this.leftImg = leftImg;
    }

    public String getRightImg() {
        return rightImg;
    }

    public void setRightImg(String rightImg) {
        this.rightImg = rightImg;
    }

    public String getLeftbackImg() {
        return leftbackImg;
    }

    public void setLeftbackImg(String leftbackImg) {
        this.leftbackImg = leftbackImg;
    }

    public String getRightbackImg() {
        return rightbackImg;
    }

    public void setRightbackImg(String rightbackImg) {
        this.rightbackImg = rightbackImg;
    }

    public String getVinImg() {
        return vinImg;
    }

    public void setVinImg(String vinImg) {
        this.vinImg = vinImg;
    }

    public String getCarMileageImg() {
        return carMileageImg;
    }

    public void setCarMileageImg(String carMileageImg) {
        this.carMileageImg = carMileageImg;
    }

    public Long getCarMileage() {
        return carMileage;
    }

    public void setCarMileage(Long carMileage) {
        this.carMileage = carMileage;
    }

    public BigDecimal getVehicleTaxPrice() {
        return vehicleTaxPrice;
    }

    public void setVehicleTaxPrice(BigDecimal vehicleTaxPrice) {
        this.vehicleTaxPrice = vehicleTaxPrice;
    }

    public BigDecimal getPurchaseTaxPrice() {
        return purchaseTaxPrice;
    }

    public void setPurchaseTaxPrice(BigDecimal purchaseTaxPrice) {
        this.purchaseTaxPrice = purchaseTaxPrice;
    }

    public String getPurchaseTaxCompleteImg() {
        return purchaseTaxCompleteImg;
    }

    public void setPurchaseTaxCompleteImg(String purchaseTaxCompleteImg) {
        this.purchaseTaxCompleteImg = purchaseTaxCompleteImg;
    }

    public BigDecimal getVehicleLicensePrice() {
        return vehicleLicensePrice;
    }

    public void setVehicleLicensePrice(BigDecimal vehicleLicensePrice) {
        this.vehicleLicensePrice = vehicleLicensePrice;
    }

    public String getVehicleLicenseInvoiceImg() {
        return vehicleLicenseInvoiceImg;
    }

    public void setVehicleLicenseInvoiceImg(String vehicleLicenseInvoiceImg) {
        this.vehicleLicenseInvoiceImg = vehicleLicenseInvoiceImg;
    }

    public String getTrafficInsuranceImg() {
        return trafficInsuranceImg;
    }

    public void setTrafficInsuranceImg(String trafficInsuranceImg) {
        this.trafficInsuranceImg = trafficInsuranceImg;
    }

    public Date getServiceEnableDate() {
        return serviceEnableDate;
    }

    public void setServiceEnableDate(Date serviceEnableDate) {
        this.serviceEnableDate = serviceEnableDate;
    }

    public Integer getServicePeriod() {
        return servicePeriod;
    }

    public void setServicePeriod(Integer servicePeriod) {
        this.servicePeriod = servicePeriod;
    }

    public Long getStoreId() {
        return storeId;
    }

    public void setStoreId(Long storeId) {
        this.storeId = storeId;
    }

    public String getStoreName() {
        return storeName;
    }

    public void setStoreName(String storeName) {
        this.storeName = storeName;
    }

    public String getPayNo() {
        return payNo;
    }

    public void setPayNo(String payNo) {
        this.payNo = payNo;
    }

    public BigDecimal getPayPrice() {
        return payPrice;
    }

    public void setPayPrice(BigDecimal payPrice) {
        this.payPrice = payPrice;
    }

    public String getPayImg() {
        return payImg;
    }

    public void setPayImg(String payImg) {
        this.payImg = payImg;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Integer getSubmitState() {
        return submitState;
    }

    public void setSubmitState(Integer submitState) {
        this.submitState = submitState;
    }

    public Integer getAuditState() {
        return auditState;
    }

    public void setAuditState(Integer auditState) {
        this.auditState = auditState;
    }

    @Override
    public String toString() {
        return "ProductOrder{" +
            "id = " + id +
            ", isDelete = " + isDelete +
            ", createdBy = " + createdBy +
            ", createdAt = " + createdAt +
            ", changedBy = " + changedBy +
            ", changedAt = " + changedAt +
            ", saleUserId = " + saleUserId +
            ", saleUserName = " + saleUserName +
            ", productId = " + productId +
            ", certificateType = " + certificateType +
            ", certificateImg = " + certificateImg +
            ", customerName = " + customerName +
            ", certificateNo = " + certificateNo +
            ", contactTelephone = " + contactTelephone +
            ", vehicleLicenseImg = " + vehicleLicenseImg +
            ", vinNo = " + vinNo +
            ", engineNo = " + engineNo +
            ", carNo = " + carNo +
            ", registrationDate = " + registrationDate +
            ", issueDate = " + issueDate +
            ", carUseage = " + carUseage +
            ", carBrandId = " + carBrandId +
            ", carSeries = " + carSeries +
            ", powerType = " + powerType +
            ", carInvoiceImg = " + carInvoiceImg +
            ", carPrice = " + carPrice +
            ", carBuyDate = " + carBuyDate +
            ", leftImg = " + leftImg +
            ", rightImg = " + rightImg +
            ", leftbackImg = " + leftbackImg +
            ", rightbackImg = " + rightbackImg +
            ", vinImg = " + vinImg +
            ", carMileageImg = " + carMileageImg +
            ", carMileage = " + carMileage +
            ", vehicleTaxPrice = " + vehicleTaxPrice +
            ", purchaseTaxPrice = " + purchaseTaxPrice +
            ", purchaseTaxCompleteImg = " + purchaseTaxCompleteImg +
            ", vehicleLicensePrice = " + vehicleLicensePrice +
            ", vehicleLicenseInvoiceImg = " + vehicleLicenseInvoiceImg +
            ", trafficInsuranceImg = " + trafficInsuranceImg +
            ", serviceEnableDate = " + serviceEnableDate +
            ", servicePeriod = " + servicePeriod +
            ", storeId = " + storeId +
            ", storeName = " + storeName +
            ", payNo = " + payNo +
            ", payPrice = " + payPrice +
            ", payImg = " + payImg +
            ", remark = " + remark +
            ", submitState = " + submitState +
            ", auditState = " + auditState +
            "}";
    }
}
