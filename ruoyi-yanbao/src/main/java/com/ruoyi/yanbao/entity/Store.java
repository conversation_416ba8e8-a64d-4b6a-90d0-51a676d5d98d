package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 经销商表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@TableName("p_store")
public class Store implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 经销商简称
     */
    private String name;

    /**
     * 经销商全称
     */
    private String fullName;

    /**
     * 是否删除：1：正常，0：删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 排序值默认0
     */
    private Integer sort;

    /**
     * 状态：1：正常：0：停用
     */
    private Integer status;

    /**
     * 经营品牌，多个品牌逗号分隔
     */
    private String suitCarBrandIds;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFullName() {
        return fullName;
    }

    public void setFullName(String fullName) {
        this.fullName = fullName;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public String getSuitCarBrandIds() {
        return suitCarBrandIds;
    }

    public void setSuitCarBrandIds(String suitCarBrandIds) {
        this.suitCarBrandIds = suitCarBrandIds;
    }

    @Override
    public String toString() {
        return "Store{" +
            "id = " + id +
            ", name = " + name +
            ", fullName = " + fullName +
            ", isDelete = " + isDelete +
            ", createdAt = " + createdAt +
            ", createdBy = " + createdBy +
            ", changedAt = " + changedAt +
            ", changedBy = " + changedBy +
            ", sort = " + sort +
            ", status = " + status +
            ", suitCarBrandIds = " + suitCarBrandIds +
            "}";
    }
}
