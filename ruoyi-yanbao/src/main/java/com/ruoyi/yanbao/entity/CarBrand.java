package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@TableName("p_car_brand")
public class CarBrand implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1：已删除，0：未删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 品牌
     */
    private String name;

    /**
     * 品牌开头字母
     */
    private String firstLetter;

    /**
     * 状态：1：正常：0：停用
     */
    private Integer status;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 汽车之家品牌ID
     */
    private Long autohomeBrandId;

    /**
     * 排序字段（非数据库字段，用于排序）
     */
    @TableField(exist = false)
    private String orderByColumn;

    /**
     * 排序方向（非数据库字段，用于排序）
     */
    @TableField(exist = false)
    private String isAsc;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getFirstLetter() {
        return firstLetter;
    }

    public void setFirstLetter(String firstLetter) {
        this.firstLetter = firstLetter;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public Long getAutohomeBrandId() {
        return autohomeBrandId;
    }

    public void setAutohomeBrandId(Long autohomeBrandId) {
        this.autohomeBrandId = autohomeBrandId;
    }

    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(String isAsc) {
        this.isAsc = isAsc;
    }

    @Override
    public String toString() {
        return "CarBrand{" +
                "id=" + id +
                ", isDelete=" + isDelete +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                ", changedBy='" + changedBy + '\'' +
                ", changedAt=" + changedAt +
                ", name='" + name + '\'' +
                ", firstLetter='" + firstLetter + '\'' +
                ", status=" + status +
                ", sort=" + sort +
                ", autohomeBrandId=" + autohomeBrandId +
                ", orderByColumn='" + orderByColumn + '\'' +
                ", isAsc='" + isAsc + '\'' +
                '}';
    }
}
