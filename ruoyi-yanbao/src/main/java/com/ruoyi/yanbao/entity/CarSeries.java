package com.ruoyi.yanbao.entity;

import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;

import java.io.Serializable;
import java.util.Date;

/**
 * <p>
 * 
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@TableName("p_car_series")
public class CarSeries implements Serializable {

    private static final long serialVersionUID = 1L;

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 1：已删除，0：未删除
     */
    @TableLogic
    @TableField(fill = FieldFill.INSERT)
    private Integer isDelete;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private Date createdAt;

    /**
     * 修改人
     */
    private String changedBy;

    /**
     * 修改时间
     */
    @TableField(fill = FieldFill.UPDATE)
    private Date changedAt;

    /**
     * 车系
     */
    private String name;

    /**
     * 品牌ID
     */
    private Long carBrandId;

    /**
     * 状态：1：正常：0：停用
     */
    private Integer status;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 汽车之家对应的车系ID
     */
    private Long autohomeSeriesId;

    /**
     * 级别：轿车，SUV，MPV
     */
    private String levelName;

    /**
     * 动力类型：1：燃油（油混）汽车，2：纯电动汽车，3：插电混合动力汽车，4：增程式电动汽车
     */
    private Integer powerType;

    /**
     * 是否国产（1：国产，2：进口）
     */
    private Integer isDomestic;

    /**
     * 品牌名称（非数据库字段，用于显示）
     */
    @TableField(exist = false)
    private String brandName;

    /**
     * 排序字段（非数据库字段，用于排序）
     */
    @TableField(exist = false)
    private String orderByColumn;

    /**
     * 排序方向（非数据库字段，用于排序）
     */
    @TableField(exist = false)
    private String isAsc;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public String getChangedBy() {
        return changedBy;
    }

    public void setChangedBy(String changedBy) {
        this.changedBy = changedBy;
    }

    public Date getChangedAt() {
        return changedAt;
    }

    public void setChangedAt(Date changedAt) {
        this.changedAt = changedAt;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Long getCarBrandId() {
        return carBrandId;
    }

    public void setCarBrandId(Long carBrandId) {
        this.carBrandId = carBrandId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getSort() {
        return sort;
    }

    public void setSort(Integer sort) {
        this.sort = sort;
    }

    public String getBrandName() {
        return brandName;
    }

    public void setBrandName(String brandName) {
        this.brandName = brandName;
    }

    public String getOrderByColumn() {
        return orderByColumn;
    }

    public void setOrderByColumn(String orderByColumn) {
        this.orderByColumn = orderByColumn;
    }

    public String getIsAsc() {
        return isAsc;
    }

    public void setIsAsc(String isAsc) {
        this.isAsc = isAsc;
    }

    public Long getAutohomeSeriesId() {
        return autohomeSeriesId;
    }

    public void setAutohomeSeriesId(Long autohomeSeriesId) {
        this.autohomeSeriesId = autohomeSeriesId;
    }

    public String getLevelName() {
        return levelName;
    }

    public void setLevelName(String levelName) {
        this.levelName = levelName;
    }

    public Integer getPowerType() {
        return powerType;
    }

    public void setPowerType(Integer powerType) {
        this.powerType = powerType;
    }

    public Integer getIsDomestic() {
        return isDomestic;
    }

    public void setIsDomestic(Integer isDomestic) {
        this.isDomestic = isDomestic;
    }

    @Override
    public String toString() {
        return "CarSeries{" +
                "id=" + id +
                ", isDelete=" + isDelete +
                ", createdBy='" + createdBy + '\'' +
                ", createdAt=" + createdAt +
                ", changedBy='" + changedBy + '\'' +
                ", changedAt=" + changedAt +
                ", name='" + name + '\'' +
                ", carBrandId=" + carBrandId +
                ", status=" + status +
                ", sort=" + sort +
                ", autohomeSeriesId=" + autohomeSeriesId +
                ", levelName='" + levelName + '\'' +
                ", powerType=" + powerType +
                ", isDomestic=" + isDomestic +
                ", brandName='" + brandName + '\'' +
                ", orderByColumn='" + orderByColumn + '\'' +
                ", isAsc='" + isAsc + '\'' +
                '}';
    }
}
