package com.ruoyi.yanbao.service.impl;

import java.util.List;
import java.util.Date;

import com.ruoyi.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.yanbao.entity.CarSeries;
import com.ruoyi.yanbao.entity.CarBrand;
import com.ruoyi.yanbao.mapper.CarSeriesMapper;
import com.ruoyi.yanbao.mapper.CarBrandMapper;
import com.ruoyi.yanbao.service.CarSeriesService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 车系管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class CarSeriesServiceImpl extends ServiceImpl<CarSeriesMapper, CarSeries> implements CarSeriesService {

    @Autowired
    private CarSeriesMapper carSeriesMapper;

    @Autowired
    private CarBrandMapper carBrandMapper;


    /**
     * 查询车系管理列表
     *
     * @param carSeries 车系管理
     * @return 车系管理
     */
    @Override
    public List<CarSeries> selectCarSeriesList(CarSeries carSeries) {
        QueryWrapper<CarSeries> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotEmpty(carSeries.getName())) {
            queryWrapper.like("name", carSeries.getName());
        }
        if (carSeries.getCarBrandId() != null) {
            queryWrapper.eq("car_brand_id", carSeries.getCarBrandId());
        }
        if (carSeries.getStatus() != null) {
            queryWrapper.eq("status", carSeries.getStatus());
        }

        // 处理排序
        String orderByColumn = carSeries.getOrderByColumn();
        String isAsc = carSeries.getIsAsc();
        if (StringUtils.isNotEmpty(orderByColumn) && StringUtils.isNotEmpty(isAsc)) {
            String orderBy = SqlUtil.escapeOrderBySql(orderByColumn);
            if ("asc".equals(isAsc)) {
                queryWrapper.orderByAsc(orderBy);
            } else {
                queryWrapper.orderByDesc(orderBy);
            }
        } else {
            queryWrapper.orderByDesc("status").orderByAsc("sort").orderByDesc("id");
        }

        return carSeriesMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除车系管理
     *
     * @param ids 需要删除的车系管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCarSeriesByIds(Long[] ids) {
        int n = 0;
        for (Long id : ids) {
            CarSeries cs = new CarSeries();
            cs.setId(id);
            cs.setChangedAt(new Date());
            cs.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            cs.setIsDelete(1);
            n = n + carSeriesMapper.updateById(cs);
        }
        return n;
    }

    /**
     * 校验车系名称是否唯一
     *
     * @param carSeries 车系信息
     * @return 结果
     */
    @Override
    public boolean checkCarSeriesNameUnique(CarSeries carSeries) {
        Long carSeriesId = StringUtils.isNull(carSeries.getId()) ? -1L : carSeries.getId();
        QueryWrapper<CarSeries> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", carSeries.getName());
        if (carSeries.getCarBrandId() != null) {
            queryWrapper.eq("car_brand_id", carSeries.getCarBrandId());
        }
        CarSeries info = carSeriesMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != carSeriesId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询所有车系
     *
     * @return 车系列表
     */
    @Override
    public List<CarSeries> selectCarSeriesAll() {
        QueryWrapper<CarSeries> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("status", 1); // 只查询启用状态的车系
        queryWrapper.orderByAsc("sort").orderByAsc("name");
        return carSeriesMapper.selectList(queryWrapper);
    }

    /**
     * 查询车系列表（带品牌信息）
     *
     * @param carSeries 车系管理
     * @return 车系管理集合（包含品牌名称）
     */
    @Override
    public List<CarSeries> selectCarSeriesListWithBrand(CarSeries carSeries) {
        List<CarSeries> carSeriesList = selectCarSeriesList(carSeries);

        // 查询所有品牌信息
        List<CarBrand> brandList = carBrandMapper.selectList(new QueryWrapper<>());

        // 为车系设置品牌名称
        for (CarSeries series : carSeriesList) {
            for (CarBrand brand : brandList) {
                if (brand.getId().equals(series.getCarBrandId())) {
                    series.setBrandName(brand.getName());
                    break;
                }
            }
        }

        return carSeriesList;
    }

    /**
     * 批量更新车系排序
     *
     * @param carSeriesList 车系列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCarSeriesSort(List<CarSeries> carSeriesList) {
        int result = 0;
        for (CarSeries carSeries : carSeriesList) {
            carSeries.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            result += carSeriesMapper.updateById(carSeries);
        }
        return result;
    }
}
