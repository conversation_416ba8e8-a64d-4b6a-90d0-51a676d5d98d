package com.ruoyi.yanbao.service.impl;

import java.util.List;
import java.util.Date;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.yanbao.entity.CarSeries;
import com.ruoyi.yanbao.mapper.CarSeriesMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.sql.SqlUtil;
import com.ruoyi.yanbao.entity.CarBrand;
import com.ruoyi.yanbao.mapper.CarBrandMapper;
import com.ruoyi.yanbao.service.CarBrandService;
import org.springframework.transaction.annotation.Transactional;

/**
 * 品牌管理 服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
@Service
public class CarBrandServiceImpl extends ServiceImpl<CarBrandMapper, CarBrand> implements CarBrandService {

    @Autowired
    private CarBrandMapper carBrandMapper;

    @Autowired
    private CarSeriesMapper carSeriesMapper;

    /**
     * 查询品牌管理列表
     *
     * @param carBrand 品牌管理
     * @return 品牌管理
     */
    @Override
    public List<CarBrand> selectCarBrandList(CarBrand carBrand) {
        QueryWrapper<CarBrand> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotEmpty(carBrand.getName())) {
            queryWrapper.like("name", carBrand.getName());
        }
        if (carBrand.getStatus() != null) {
            queryWrapper.eq("status", carBrand.getStatus());
        }
        if (StringUtils.isNotEmpty(carBrand.getFirstLetter())) {
            queryWrapper.eq("first_letter", carBrand.getFirstLetter());
        }

        // 处理排序
        String orderByColumn = carBrand.getOrderByColumn();
        String isAsc = carBrand.getIsAsc();
        if (StringUtils.isNotEmpty(orderByColumn) && StringUtils.isNotEmpty(isAsc)) {
            String orderBy = SqlUtil.escapeOrderBySql(orderByColumn);
            if ("asc".equals(isAsc)) {
                queryWrapper.orderByAsc(orderBy);
            } else {
                queryWrapper.orderByDesc(orderBy);
            }
        } else {
            queryWrapper.orderByAsc("sort").orderByDesc("created_at");
        }

        return carBrandMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除品牌管理
     *
     * @param ids 需要删除的品牌管理主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteCarBrandByIds(Long[] ids) {
        int n = 0;
        for (Long id : ids) {
            CarBrand cb = new CarBrand();
            cb.setId(id);
            cb.setChangedAt(new Date());
            cb.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            cb.setIsDelete(1);
            n = n + carBrandMapper.updateById(cb);
        }
        return n;
    }

    /**
     * 校验品牌名称是否唯一
     *
     * @param carBrand 品牌信息
     * @return 结果
     */
    @Override
    public boolean checkCarBrandNameUnique(CarBrand carBrand) {
        Long carBrandId = StringUtils.isNull(carBrand.getId()) ? -1L : carBrand.getId();
        QueryWrapper<CarBrand> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", carBrand.getName());
        CarBrand info = carBrandMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != carBrandId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询所有品牌
     *
     * @return 品牌列表
     */
    @Override
    public List<CarBrand> selectCarBrandAll(Integer status) {
        QueryWrapper<CarBrand> queryWrapper = new QueryWrapper<>();
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("status").orderByDesc("sort").orderByAsc("name");
        return carBrandMapper.selectList(queryWrapper);
    }

    /**
     * 批量更新品牌排序
     *
     * @param carBrandList 品牌列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateCarBrandSort(List<CarBrand> carBrandList) {
        int result = 0;
        for (CarBrand carBrand : carBrandList) {
            carBrand.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            result += carBrandMapper.updateById(carBrand);
        }
        return result;
    }

    @Override
    public boolean changeStatus(Long id, Integer status) {
        CarBrand carBrand = getById(id);
        if (carBrand != null) {
            carBrand.setStatus(status);
            carBrand.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            if (status == 0) {
                LambdaQueryWrapper<CarSeries> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(CarSeries::getCarBrandId, id);
                queryWrapper.eq(CarSeries::getStatus, 1);
                List<CarSeries> carSeries = carSeriesMapper.selectList(queryWrapper);
                if (!carSeries.isEmpty()) {
                    for (CarSeries cs : carSeries) {
                        cs.setStatus(status);
                        cs.setChangedBy(SecurityUtils.getLoginUser().getUsername());
                        carSeriesMapper.updateById(cs);
                    }
                }
            }
            return updateById(carBrand);
        }
        return false;
    }
}
