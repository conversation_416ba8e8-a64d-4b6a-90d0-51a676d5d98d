package com.ruoyi.yanbao.service.impl;

import java.util.List;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.yanbao.entity.Store;
import com.ruoyi.yanbao.mapper.StoreMapper;
import com.ruoyi.yanbao.service.StoreService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 经销商表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-02
 */
@Service
public class StoreServiceImpl extends ServiceImpl<StoreMapper, Store> implements StoreService {

    @Autowired
    private StoreMapper storeMapper;

    /**
     * 查询经销商列表
     *
     * @param store 经销商
     * @return 经销商
     */
    @Override
    public List<Store> selectStoreList(Store store) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();

        if (StringUtils.isNotEmpty(store.getName())) {
            queryWrapper.like("name", store.getName());
        }
        if (StringUtils.isNotEmpty(store.getFullName())) {
            queryWrapper.like("full_name", store.getFullName());
        }
        if (store.getStatus() != null) {
            queryWrapper.eq("status", store.getStatus());
        }

        queryWrapper.orderByDesc("status").orderByAsc("sort").orderByAsc("name");
        return storeMapper.selectList(queryWrapper);
    }

    /**
     * 批量删除经销商
     *
     * @param ids 需要删除的经销商主键集合
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteStoreByIds(Long[] ids) {
        int result = 0;
        for (Long id : ids) {
            Store store = getById(id);
            if (store != null) {
                store.setChangedBy(SecurityUtils.getLoginUser().getUsername());
                result += storeMapper.deleteById(id);
            }
        }
        return result;
    }

    /**
     * 校验经销商名称是否唯一
     *
     * @param store 经销商信息
     * @return 结果
     */
    @Override
    public boolean checkStoreNameUnique(Store store) {
        Long storeId = StringUtils.isNull(store.getId()) ? -1L : store.getId();
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("name", store.getName());
        Store info = storeMapper.selectOne(queryWrapper);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != storeId.longValue()) {
            return false;
        }
        return true;
    }

    /**
     * 查询所有经销商
     *
     * @param status 状态
     * @return 经销商列表
     */
    @Override
    public List<Store> selectStoreAll(Integer status) {
        QueryWrapper<Store> queryWrapper = new QueryWrapper<>();
        if (status != null) {
            queryWrapper.eq("status", status);
        }
        queryWrapper.orderByDesc("status").orderByAsc("sort").orderByAsc("name");
        return storeMapper.selectList(queryWrapper);
    }

    /**
     * 修改经销商状态
     *
     * @param id 经销商ID
     * @param status 状态
     * @return 结果
     */
    @Override
    public boolean changeStatus(Long id, Integer status) {
        Store store = getById(id);
        if (store != null) {
            store.setStatus(status);
            store.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            return updateById(store);
        }
        return false;
    }

    /**
     * 批量更新经销商排序
     *
     * @param storeList 经销商列表
     * @return 结果
     */
    @Override
    @Transactional
    public int updateStoreSort(List<Store> storeList) {
        int result = 0;
        for (Store store : storeList) {
            store.setChangedBy(SecurityUtils.getLoginUser().getUsername());
            result += storeMapper.updateById(store);
        }
        return result;
    }
}
