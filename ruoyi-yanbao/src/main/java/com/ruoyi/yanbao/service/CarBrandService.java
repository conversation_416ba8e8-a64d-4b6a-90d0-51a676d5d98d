package com.ruoyi.yanbao.service;

import java.util.List;
import com.ruoyi.yanbao.entity.CarBrand;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 品牌管理 服务类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface CarBrandService extends IService<CarBrand> {

    /**
     * 查询品牌管理列表
     *
     * @param carBrand 品牌管理
     * @return 品牌管理集合
     */
    public List<CarBrand> selectCarBrandList(CarBrand carBrand);

    /**
     * 批量删除品牌管理
     *
     * @param ids 需要删除的品牌管理主键集合
     * @return 结果
     */
    public int deleteCarBrandByIds(Long[] ids);

    /**
     * 校验品牌名称是否唯一
     *
     * @param carBrand 品牌信息
     * @return 结果
     */
    public boolean checkCarBrandNameUnique(CarBrand carBrand);

    /**
     * 查询所有品牌
     *
     * @return 品牌列表
     */
    public List<CarBrand> selectCarBrandAll(Integer status);

    /**
     * 批量更新品牌排序
     *
     * @param carBrandList 品牌列表
     * @return 结果
     */
    public int updateCarBrandSort(List<CarBrand> carBrandList);

    /**
     * 启用，停用
     * @param id
     * @param status
     * @return
     */
    boolean changeStatus(Long id,Integer status);
}
