package com.ruoyi.yanbao.service;

import java.util.List;
import com.ruoyi.yanbao.entity.CarSeries;
import com.baomidou.mybatisplus.extension.service.IService;

/**
 * 车系管理 服务类
 *
 * <AUTHOR>
 * @since 2025-06-12
 */
public interface CarSeriesService extends IService<CarSeries> {

    /**
     * 查询车系管理列表
     *
     * @param carSeries 车系管理
     * @return 车系管理集合
     */
    public List<CarSeries> selectCarSeriesList(CarSeries carSeries);

    /**
     * 批量删除车系管理
     *
     * @param ids 需要删除的车系管理主键集合
     * @return 结果
     */
    public int deleteCarSeriesByIds(Long[] ids);

    /**
     * 校验车系名称是否唯一
     *
     * @param carSeries 车系信息
     * @return 结果
     */
    public boolean checkCarSeriesNameUnique(CarSeries carSeries);

    /**
     * 查询所有车系
     *
     * @return 车系列表
     */
    public List<CarSeries> selectCarSeriesAll();

    /**
     * 查询车系列表（带品牌信息）
     *
     * @param carSeries 车系管理
     * @return 车系管理集合（包含品牌名称）
     */
    public List<CarSeries> selectCarSeriesListWithBrand(CarSeries carSeries);

    /**
     * 批量更新车系排序
     *
     * @param carSeriesList 车系列表
     * @return 结果
     */
    public int updateCarSeriesSort(List<CarSeries> carSeriesList);
}
