# 经销商管理功能说明

## 功能概述

本次为若依框架的yanbao模块添加了完整的经销商管理功能，包括后端API和前端管理页面。

## 已创建的文件

### 后端文件

1. **StoreController.java** - 经销商管理控制器
   - 路径：`ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/StoreController.java`
   - 功能：提供经销商的增删改查、状态修改、排序等API接口

2. **StoreService.java** - 经销商服务接口（已修改）
   - 路径：`ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/StoreService.java`
   - 功能：定义经销商业务方法接口

3. **StoreServiceImpl.java** - 经销商服务实现类（已修改）
   - 路径：`ruoyi-yanbao/src/main/java/com/ruoyi/yanbao/service/impl/StoreServiceImpl.java`
   - 功能：实现经销商业务逻辑

### 前端文件

4. **store.js** - 前端API调用文件
   - 路径：`ruoyi-ui/src/api/system/store.js`
   - 功能：封装经销商相关的HTTP请求

5. **index.vue** - 经销商管理页面
   - 路径：`ruoyi-ui/src/views/system/store/index.vue`
   - 功能：经销商列表展示、搜索、新增、修改、删除等操作界面

### 数据库脚本

6. **store_menu.sql** - 菜单权限SQL脚本
   - 路径：`sql/store_menu.sql`
   - 功能：添加经销商管理菜单和相关权限按钮

## 功能特性

### 主要功能
- ✅ 经销商列表查询（支持分页）
- ✅ 经销商信息搜索（按名称、全称、状态）
- ✅ 新增经销商
- ✅ 修改经销商信息
- ✅ 删除经销商（支持批量删除）
- ✅ 经销商状态切换（启用/停用）
- ✅ 经销商排序功能
- ✅ 经销商名称唯一性校验

### 权限控制
- 查询权限：`system:store:list`
- 详情权限：`system:store:query`
- 新增权限：`system:store:add`
- 修改权限：`system:store:edit`
- 删除权限：`system:store:remove`
- 导出权限：`system:store:export`

## 部署步骤

### 1. 执行数据库脚本
```sql
-- 执行菜单权限脚本
source sql/store_menu.sql;
```

### 2. 重启后端服务
重启Spring Boot应用以加载新的Controller

### 3. 重新构建前端
```bash
cd ruoyi-ui
npm run build:prod
```

### 4. 配置菜单权限
1. 登录系统管理员账号
2. 进入"系统管理" -> "菜单管理"
3. 确认"经销商管理"菜单已添加
4. 进入"系统管理" -> "角色管理"
5. 为相应角色分配经销商管理权限

## 使用说明

### 访问路径
- 前端访问路径：`/system/store`
- 后端API基础路径：`/system/store`

### 主要API接口
- `GET /system/store/list` - 获取经销商列表
- `GET /system/store/{id}` - 获取经销商详情
- `POST /system/store` - 新增经销商
- `PUT /system/store` - 修改经销商
- `DELETE /system/store/{ids}` - 删除经销商
- `PUT /system/store/changeStatus` - 修改经销商状态
- `GET /system/store/optionSelect` - 获取经销商选项列表

### 数据字段说明
- `id`: 主键ID
- `name`: 经销商简称（必填，唯一）
- `fullName`: 经销商全称（必填）
- `sort`: 排序值（默认0）
- `status`: 状态（1-正常，0-停用）
- `suitCarBrandIds`: 经营品牌ID（多个用逗号分隔）
- `createdAt`: 创建时间
- `createdBy`: 创建人
- `changedAt`: 修改时间
- `changedBy`: 修改人

## 注意事项

1. 经销商名称必须唯一
2. 删除经销商会进行逻辑删除，不会物理删除数据
3. 状态修改会实时生效
4. 支持批量操作（删除）
5. 所有操作都有权限控制，需要相应权限才能执行

## 扩展建议

1. 可以添加经销商与品牌的关联管理
2. 可以添加经销商地区信息
3. 可以添加经销商联系方式等详细信息
4. 可以添加经销商业绩统计功能
